package com.github.cret.web.oee.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.ExampleMatcher.GenericPropertyMatchers;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import com.github.cret.web.common.domain.PageList;
import com.github.cret.web.common.domain.PageableParam;
import com.github.cret.web.common.enumerate.SysErrEnum;
import com.github.cret.web.common.exception.SystemException;
import com.github.cret.web.common.util.JacksonUtil;
import com.github.cret.web.oee.config.FeedbackConfig;
import com.github.cret.web.oee.config.WxWorkConfig;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerRecord;
import com.github.cret.web.oee.document.feedback.FeedbackTriggerSend;
import com.github.cret.web.oee.document.feedback.Reporter;
import com.github.cret.web.oee.document.feedback.Responder;
import com.github.cret.web.oee.document.feedback.ResponseConfig;
import com.github.cret.web.oee.domain.request.FeedbackUrlParams;
import com.github.cret.web.oee.domain.wxwork.MsgRes;
import com.github.cret.web.oee.domain.wxwork.R;
import com.github.cret.web.oee.domain.wxwork.TextCard;
import com.github.cret.web.oee.domain.wxwork.TextCardContent;
import com.github.cret.web.oee.repository.FeedbackTriggerSendRepository;
import com.github.cret.web.oee.service.FeedbackTriggerSendService;
import com.github.cret.web.oee.utils.BuilderUtil;
import com.github.cret.web.oee.utils.WxWorkMessageUtil;

/**
 * 反馈触发发送服务实现
 */
@Service
public class FeedbackTriggerSendServiceImpl implements FeedbackTriggerSendService {

	private static final Logger logger = LoggerFactory.getLogger(FeedbackTriggerSendServiceImpl.class);

	private final FeedbackTriggerSendRepository repository;

	private final WxWorkConfig wxWorkConfig;

	private final FeedbackConfig feedbackConfig;

	private final RestTemplate restTemplate;

	private final MongoTemplate mongoTemplate;

	public FeedbackTriggerSendServiceImpl(FeedbackTriggerSendRepository repository, WxWorkConfig wxWorkConfig,
			FeedbackConfig feedbackConfig, RestTemplate restTemplate, MongoTemplate mongoTemplate) {
		this.repository = repository;
		this.wxWorkConfig = wxWorkConfig;
		this.feedbackConfig = feedbackConfig;
		this.restTemplate = restTemplate;
		this.mongoTemplate = mongoTemplate;
	}

	@Override
	public void processScheduledSends() {
		Date currentTime = new Date();

		// 查找所有到期且未发送的记录
		List<FeedbackTriggerSend> pendingSends = repository
			.findByExpectedSendTimeLessThanEqualAndSendStatus(currentTime, false);

		if (pendingSends.isEmpty()) {
			logger.debug("没有需要发送的反馈触发记录");
			return;
		}

		logger.info("开始处理定时发送任务，共找到 {} 条待发送记录", pendingSends.size());

		// 计算1分钟前的时间点
		long oneMinuteInMillis = 60 * 1000L;
		Date oneMinuteAgo = new Date(currentTime.getTime() - oneMinuteInMillis);

		int successCount = 0;
		int failureCount = 0;
		int expiredCount = 0;

		for (FeedbackTriggerSend sendRecord : pendingSends) {
			try {
				Date expectedSendTime = sendRecord.getExpectedSendTime();

				// 判断是否超期：预期发送时间在1分钟前
				if (expectedSendTime.before(oneMinuteAgo)) {
					// 超期记录，标记为超期未发送
					sendRecord.setSendStatus(true);
					sendRecord.setSendTime(currentTime);
					sendRecord.setSendResult("超期未发送：预期发送时间超过1分钟");
					repository.save(sendRecord);

					expiredCount++;
					logger.warn("记录超期未发送，记录ID: {}, 预期发送时间: {}, 当前时间: {}", sendRecord.getId(), expectedSendTime,
							currentTime);
					continue;
				}

				// 在1分钟内的记录，执行发送
				String sendResult = performSend(sendRecord);

				// 更新发送状态
				sendRecord.setSendStatus(true);
				sendRecord.setSendTime(currentTime);
				sendRecord.setSendResult(sendResult);

				// 保存更新后的记录
				repository.save(sendRecord);

				successCount++;
				logger.debug("发送成功，记录ID: {}, 结果: {}", sendRecord.getId(), sendResult);

			}
			catch (Exception e) {
				// 发送失败，记录错误信息但不影响其他记录的处理
				sendRecord.setSendStatus(true); // 标记为已处理，避免重复发送
				sendRecord.setSendTime(currentTime);
				sendRecord.setSendResult("发送失败: " + e.getMessage());

				repository.save(sendRecord);

				failureCount++;
				logger.error("发送失败，记录ID: {}, 错误: {}", sendRecord.getId(), e.getMessage(), e);
			}
		}

		logger.info("定时发送任务完成，成功: {} 条，失败: {} 条，超期: {} 条", successCount, failureCount, expiredCount);
	}

	@Override
	public String performSend(FeedbackTriggerSend sendRecord) {
		try {
			// 1. 基础参数验证
			if (sendRecord == null) {
				logger.error("发送记录不能为空");
				return "发送失败：发送记录不能为空";
			}

			String sendRecordId = sendRecord.getId();
			String triggerRecordId = sendRecord.getTriggerRecordId();
			String sendInfo = sendRecord.getSendInfo();

			if (!StringUtils.hasText(sendRecordId) || !StringUtils.hasText(triggerRecordId)
					|| !StringUtils.hasText(sendInfo)) {
				logger.error("发送记录关键信息缺失，记录ID: {}", sendRecordId);
				return "发送失败：发送记录关键信息缺失";
			}

			// 2. 检查是否为新的单用户记录格式
			if (StringUtils.hasText(sendRecord.getUserId())) {
				// 新格式：单用户记录
				return performSingleUserSend(sendRecord);
			}
			else {
				// 旧格式：多用户记录（向后兼容）
				return performLegacyMultiUserSend(sendRecord);
			}
		}
		catch (Exception e) {
			logger.error("发送消息时发生异常，记录ID: {}", sendRecord != null ? sendRecord.getId() : "null", e);
			return "发送失败：消息发送异常 - " + e.getMessage();
		}
	}

	/**
	 * 执行单用户发送（新格式）
	 * @param sendRecord 发送记录
	 * @return 发送结果
	 */
	private String performSingleUserSend(FeedbackTriggerSend sendRecord) {
		String sendRecordId = sendRecord.getId();
		String triggerRecordId = sendRecord.getTriggerRecordId();
		String userId = sendRecord.getUserId();
		String userName = sendRecord.getUserName();
		String userType = sendRecord.getUserType();

		// 验证用户信息
		if (!StringUtils.hasText(userId)) {
			logger.warn("单用户发送记录缺少用户ID，记录ID: {}", sendRecordId);
			return "发送跳过：缺少用户ID";
		}

		try {
			// 根据用户类型确定操作类型和按钮文本
			String action = "responder".equals(userType) ? "edit" : "view";
			String buttonText = "responder".equals(userType) ? "解决异常" : "查看异常";

			// 构建包含用户信息的个性化URL
			FeedbackUrlParams feedbackUrlParams = BuilderUtil.builder(FeedbackUrlParams::new)
				.with(FeedbackUrlParams::setAction, action)
				.with(FeedbackUrlParams::setTriggerRecordId, triggerRecordId)
				.with(FeedbackUrlParams::setSendRecordId, sendRecordId)
				.with(FeedbackUrlParams::setUserId, userId)
				.with(FeedbackUrlParams::setUserName, userName)
				.build();
			String personalizedUrl = feedbackConfig.buildFeedbackUrl(feedbackUrlParams);

			// 构建文本卡片内容
			TextCardContent content = new TextCardContent();
			content.setTitle("异常通知");
			content.setDescription(sendRecord.getSendInfo());
			content.setUrl(personalizedUrl);
			content.setBtntxt(buttonText);

			// 构建文本卡片（单个用户）
			TextCard textCard = new TextCard(userId, null, null, "textcard", content, wxWorkConfig.getEnableIdTrans(),
					wxWorkConfig);

			// 序列化并发送消息
			String msg = JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().writeValueAsString(textCard));
			if (msg == null) {
				logger.error("消息序列化失败，用户ID: {}, 记录ID: {}", userId, sendRecordId);
				return "发送失败：消息序列化失败";
			}

			R<MsgRes> sendResult = WxWorkMessageUtil.sendWxWorkMsg(msg, wxWorkConfig, restTemplate);

			return WxWorkMessageUtil.buildResultText(sendResult, 1);
		}
		catch (Exception e) {
			logger.error("发送消息给用户{}时发生异常，记录ID: {}", userId, sendRecordId, e);
			return String.format("发送失败：%s", e.getMessage());
		}
	}

	/**
	 * 执行多用户发送（旧格式，向后兼容）
	 * @param sendRecord 发送记录
	 * @return 发送结果
	 */
	private String performLegacyMultiUserSend(FeedbackTriggerSend sendRecord) {
		String sendRecordId = sendRecord.getId();
		String triggerRecordId = sendRecord.getTriggerRecordId();

		// 获取有效用户列表和用户名映射
		List<String> respondUsers = new ArrayList<>();
		Map<String, String> respondUserNames = new HashMap<>();
		if (sendRecord.getResponders() != null) {
			sendRecord.getResponders()
				.stream()
				.filter(responder -> responder != null && StringUtils.hasText(responder.getRespondentId()))
				.forEach(responder -> {
					String userId = responder.getRespondentId();
					if (!respondUsers.contains(userId)) {
						respondUsers.add(userId);
						respondUserNames.put(userId, responder.getRespondentName());
					}
				});
		}

		List<String> reportUsers = new ArrayList<>();
		Map<String, String> reportUserNames = new HashMap<>();
		if (sendRecord.getReporters() != null) {
			sendRecord.getReporters()
				.stream()
				.filter(reporter -> reporter != null && StringUtils.hasText(reporter.getReporterId()))
				.forEach(reporter -> {
					String userId = reporter.getReporterId();
					if (!reportUsers.contains(userId)) {
						reportUsers.add(userId);
						reportUserNames.put(userId, reporter.getReportName());
					}
				});
		}

		// 验证接收人
		if (respondUsers.isEmpty() && reportUsers.isEmpty()) {
			logger.warn("反馈触发通知无有效接收人，记录ID: {}", sendRecordId);
			return "发送跳过：无有效接收人";
		}

		int totalRecipients = respondUsers.size() + reportUsers.size();
		if (totalRecipients > 1000) {
			logger.warn("接收人数量超过限制，记录ID: {}, 接收人数: {}", sendRecordId, totalRecipients);
			return String.format("发送失败：接收人数量超过限制（%d > 1000）", totalRecipients);
		}

		// 执行发送操作
		StringBuilder resultBuilder = new StringBuilder();
		int successCount = 0;

		// 发送给响应人（填写解决方案）
		if (!respondUsers.isEmpty()) {
			R<MsgRes> sendResult = sendToUsersLegacy(respondUsers, respondUserNames, sendRecord, triggerRecordId,
					sendRecordId, "edit", "解决异常");
			String resultText = WxWorkMessageUtil.buildResultText(sendResult, respondUsers.size());
			resultBuilder.append("响应人: ").append(resultText);
			if (sendResult != null && sendResult.isSuccess())
				successCount++;
		}

		// 发送给通知人（查看解决方案）
		if (!reportUsers.isEmpty()) {
			if (resultBuilder.length() > 0)
				resultBuilder.append("; ");
			R<MsgRes> reportResult = sendToUsersLegacy(reportUsers, reportUserNames, sendRecord, triggerRecordId,
					sendRecordId, "view", "查看异常");
			String resultText = WxWorkMessageUtil.buildResultText(reportResult, reportUsers.size());
			resultBuilder.append("通知人: ").append(resultText);
			if (reportResult != null && reportResult.isSuccess())
				successCount++;
		}

		String finalResult = String.format("发送完成 - %s", resultBuilder.toString());
		logger.info("反馈触发通知发送完成，记录ID: {}, 成功: {}, 响应人数: {}, 通知人数: {}", sendRecordId, successCount, respondUsers.size(),
				reportUsers.size());

		return finalResult;
	}

	/**
	 * 发送消息给指定用户（旧版本，向后兼容）
	 * @param users 接收用户列表
	 * @param userNames 用户名称映射
	 * @param sendRecord 发送记录
	 * @param triggerRecordId 触发记录ID
	 * @param sendRecordId 发送记录ID
	 * @param action 操作类型
	 * @param buttonText 按钮文本
	 * @return 发送结果
	 */
	private R<MsgRes> sendToUsersLegacy(List<String> users, Map<String, String> userNames,
			FeedbackTriggerSend sendRecord, String triggerRecordId, String sendRecordId, String action,
			String buttonText) {
		if (users == null || users.isEmpty()) {
			return R.fail("用户列表为空");
		}

		int successCount = 0;
		int failureCount = 0;
		List<String> invalidUsers = new ArrayList<>();
		StringBuilder errorMessages = new StringBuilder();

		// 为每个用户单独发送消息
		for (String userId : users) {
			try {
				String userName = userNames.get(userId);

				// 构建包含用户信息的个性化URL
				FeedbackUrlParams feedbackUrlParams = BuilderUtil.builder(FeedbackUrlParams::new)
					.with(FeedbackUrlParams::setAction, action)
					.with(FeedbackUrlParams::setTriggerRecordId, triggerRecordId)
					.with(FeedbackUrlParams::setSendRecordId, sendRecordId)
					.with(FeedbackUrlParams::setUserId, userId)
					.with(FeedbackUrlParams::setUserName, userName)
					.build();
				String personalizedUrl = feedbackConfig.buildFeedbackUrl(feedbackUrlParams);

				// 构建文本卡片内容
				TextCardContent content = new TextCardContent();
				content.setTitle("异常通知");
				content.setDescription(sendRecord.getSendInfo());
				content.setUrl(personalizedUrl);
				content.setBtntxt(buttonText);

				// 构建文本卡片（单个用户）
				TextCard textCard = new TextCard(userId, null, null, "textcard", content,
						wxWorkConfig.getEnableIdTrans(), wxWorkConfig);

				// 序列化并发送消息
				String msg = JacksonUtil.tryParse(() -> JacksonUtil.getObjectMapper().writeValueAsString(textCard));
				if (msg == null) {
					logger.error("消息序列化失败，用户ID: {}, 记录ID: {}", userId, sendRecordId);
					failureCount++;
					errorMessages.append(String.format("用户%s消息序列化失败; ", userId));
					continue;
				}

				R<MsgRes> singleResult = WxWorkMessageUtil.sendWxWorkMsg(msg, wxWorkConfig, restTemplate);

				if (singleResult != null && singleResult.isSuccess()) {
					MsgRes msgRes = singleResult.getData();
					if (msgRes != null && msgRes.getErrCode() != null && msgRes.getErrCode() == 0) {
						// 检查是否有无效用户
						List<String> singleInvalidUsers = msgRes.getInvalidUserList();
						if (singleInvalidUsers != null && !singleInvalidUsers.isEmpty()) {
							invalidUsers.addAll(singleInvalidUsers);
							failureCount++;
						}
						else {
							successCount++;
						}
					}
					else {
						failureCount++;
						if (msgRes != null && msgRes.getErrMsg() != null) {
							errorMessages.append(String.format("用户%s发送失败: %s; ", userId, msgRes.getErrMsg()));
						}
					}
				}
				else {
					failureCount++;
					String errorMsg = singleResult != null ? singleResult.getMsg() : "未知错误";
					errorMessages.append(String.format("用户%s发送失败: %s; ", userId, errorMsg));
				}

			}
			catch (Exception e) {
				logger.error("发送消息给用户{}时发生异常，记录ID: {}", userId, sendRecordId, e);
				failureCount++;
				errorMessages.append(String.format("用户%s发送异常: %s; ", userId, e.getMessage()));
			}
		}

		// 构建汇总结果
		MsgRes aggregatedResult = new MsgRes();
		aggregatedResult.setErrCode(successCount > 0 ? 0 : -1);

		// 将无效用户列表转换为字符串格式（用|分隔）
		if (!invalidUsers.isEmpty()) {
			aggregatedResult.setInvalidUser(String.join("|", invalidUsers));
		}

		if (successCount > 0) {
			String successMsg = String.format("成功发送%d人", successCount);
			if (failureCount > 0) {
				successMsg += String.format("，失败%d人", failureCount);
			}
			aggregatedResult.setErrMsg(successMsg);
			return R.success(aggregatedResult);
		}
		else {
			String failMsg = String.format("全部发送失败（%d人）", failureCount);
			if (errorMessages.length() > 0) {
				failMsg += ": " + errorMessages.toString();
			}
			aggregatedResult.setErrMsg(failMsg);
			return R.fail(failMsg);
		}
	}

	// ==================== 创建发送记录方法 ====================

	/**
	 * 创建反馈触发发送记录（为每个用户创建单独记录）
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 */
	public void createFeedbackTriggerSend(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig) {
		try {
			// 计算预期发送时间
			Date expectedSendTime = triggerRecord.getTriggerTime();
			if (responseConfig.getPointTime() != null && responseConfig.getPointTime() > 0) {
				// 将pointTime（分钟）转换为毫秒并加到触发时间上
				long pointTimeMillis = responseConfig.getPointTime() * 60 * 1000L;
				expectedSendTime = new Date(triggerRecord.getTriggerTime().getTime() + pointTimeMillis);
			}

			// 构建发送信息
			String sendInfo = buildSendInfo(triggerRecord, responseConfig);

			// 为每个响应人创建单独的发送记录
			if (responseConfig.getResponders() != null) {
				for (Responder responder : responseConfig.getResponders()) {
					if (responder != null && responder.getRespondentId() != null
							&& !responder.getRespondentId().trim().isEmpty()) {
						createSingleUserSendRecord(triggerRecord.getId(), expectedSendTime, sendInfo,
								responder.getRespondentId(), responder.getRespondentName(), "responder");
					}
				}
			}

			// 为每个告知人创建单独的发送记录
			if (responseConfig.getReporters() != null) {
				for (Reporter reporter : responseConfig.getReporters()) {
					if (reporter != null && reporter.getReporterId() != null
							&& !reporter.getReporterId().trim().isEmpty()) {
						createSingleUserSendRecord(triggerRecord.getId(), expectedSendTime, sendInfo,
								reporter.getReporterId(), reporter.getReportName(), "reporter");
					}
				}
			}

		}
		catch (Exception e) {
			// 记录发送失败的情况，但不影响主流程
			createFailedSendRecord(triggerRecord, responseConfig, e);
		}
	}

	/**
	 * 为单个用户创建发送记录
	 * @param triggerRecordId 触发记录ID
	 * @param expectedSendTime 预期发送时间
	 * @param sendInfo 发送信息
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @param userType 用户类型（responder/reporter）
	 */
	private void createSingleUserSendRecord(String triggerRecordId, Date expectedSendTime, String sendInfo,
			String userId, String userName, String userType) {
		try {
			FeedbackTriggerSend sendRecord = new FeedbackTriggerSend();

			// 设置基本信息
			sendRecord.setTriggerRecordId(triggerRecordId);
			sendRecord.setExpectedSendTime(expectedSendTime);
			sendRecord.setSendInfo(sendInfo);

			// 设置单个用户信息
			sendRecord.setUserId(userId);
			sendRecord.setUserName(userName);
			sendRecord.setUserType(userType);

			// 初始状态：未发送
			sendRecord.setSendStatus(false);

			// 保存发送记录，等待定时任务处理
			repository.save(sendRecord);

		}
		catch (Exception e) {
			// 单个用户记录创建失败，记录错误但不影响其他用户
			createFailedSingleUserRecord(triggerRecordId, expectedSendTime, userId, userName, userType, e);
		}
	}

	/**
	 * 创建失败的发送记录
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 * @param e 异常信息
	 */
	private void createFailedSendRecord(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig,
			Exception e) {
		try {
			FeedbackTriggerSend failedRecord = new FeedbackTriggerSend();
			failedRecord.setTriggerRecordId(triggerRecord.getId());

			// 设置预期发送时间为触发时间加上pointTime（分钟）
			Date expectedSendTime = triggerRecord.getTriggerTime();
			if (responseConfig.getPointTime() != null && responseConfig.getPointTime() > 0) {
				long pointTimeMillis = responseConfig.getPointTime() * 60 * 1000L;
				expectedSendTime = new Date(triggerRecord.getTriggerTime().getTime() + pointTimeMillis);
			}
			failedRecord.setExpectedSendTime(expectedSendTime);

			failedRecord.setSendResult("创建失败: " + e.getMessage());
			failedRecord.setSendInfo("系统异常，创建发送记录失败");
			failedRecord.setSendStatus(false);
			repository.save(failedRecord);
		}
		catch (Exception saveException) {
			// 连保存失败记录都失败了，只能记录日志
			logger.error("保存失败记录时发生异常: {}", saveException.getMessage(), saveException);
		}
	}

	/**
	 * 创建单个用户的失败记录
	 * @param triggerRecordId 触发记录ID
	 * @param expectedSendTime 预期发送时间
	 * @param userId 用户ID
	 * @param userName 用户名称
	 * @param userType 用户类型
	 * @param e 异常信息
	 */
	private void createFailedSingleUserRecord(String triggerRecordId, Date expectedSendTime, String userId,
			String userName, String userType, Exception e) {
		try {
			FeedbackTriggerSend failedRecord = new FeedbackTriggerSend();
			failedRecord.setTriggerRecordId(triggerRecordId);
			failedRecord.setExpectedSendTime(expectedSendTime);
			failedRecord.setUserId(userId);
			failedRecord.setUserName(userName);
			failedRecord.setUserType(userType);
			failedRecord.setSendResult("创建失败: " + e.getMessage());
			failedRecord.setSendInfo("系统异常，创建单个用户发送记录失败");
			failedRecord.setSendStatus(false);
			repository.save(failedRecord);
		}
		catch (Exception saveException) {
			// 连保存失败记录都失败了，只能记录日志
			logger.error("保存单个用户失败记录时发生异常: {}", saveException.getMessage(), saveException);
		}
	}

	/**
	 * 构建发送信息
	 * @param triggerRecord 触发记录
	 * @param responseConfig 响应配置
	 * @return 发送信息
	 */
	private String buildSendInfo(FeedbackTriggerRecord triggerRecord, ResponseConfig responseConfig) {
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");

		return """
				异常告警通知
				线体编码: %s
				异常名称：%s
				异常详情：%s
				触发时间: %s
				触发人: %s
				响应时间要求: %s""".formatted(triggerRecord.getLineCode(), triggerRecord.getAnomaliesName(),
				triggerRecord.getAnomaliesDetail(), dateFormat.format(triggerRecord.getTriggerTime()),
				triggerRecord.getTriggerUserName(), responseConfig.getPointTime() + "分钟内");
	}

	// ==================== 基础CRUD操作 ====================

	@Override
	public FeedbackTriggerSend save(FeedbackTriggerSend feedbackTriggerSend) {
		return repository.save(feedbackTriggerSend);
	}

	@Override
	public FeedbackTriggerSend findById(String id) {
		return repository.findById(id).orElseThrow(() -> new SystemException(SysErrEnum.NOT_FOUND));
	}

	@Override
	public PageList<FeedbackTriggerSend> page(PageableParam<FeedbackTriggerSend> param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("triggerRecordId", GenericPropertyMatchers.exact())
			.withMatcher("sendStatus", GenericPropertyMatchers.exact())
			.withMatcher("sendResult", GenericPropertyMatchers.contains())
			.withMatcher("sendInfo", GenericPropertyMatchers.contains())
			.withIgnorePaths("_class");
		Example<FeedbackTriggerSend> example = Example.of(param.getSearchParams(), matcher);
		return PageList.fromPage(repository.findAll(example, param.getPageRequest()));
	}

	@Override
	public List<FeedbackTriggerSend> findList(FeedbackTriggerSend param) {
		ExampleMatcher matcher = ExampleMatcher.matching()
			.withIgnoreNullValues()
			.withMatcher("triggerRecordId", GenericPropertyMatchers.exact())
			.withMatcher("sendStatus", GenericPropertyMatchers.exact())
			.withMatcher("sendResult", GenericPropertyMatchers.contains())
			.withMatcher("sendInfo", GenericPropertyMatchers.contains());
		Example<FeedbackTriggerSend> example = Example.of(param, matcher);
		return repository.findAll(example);
	}

	@Override
	public List<FeedbackTriggerSend> findAll() {
		return mongoTemplate.findAll(FeedbackTriggerSend.class);
	}

	@Override
	public void deleteById(String id) {
		FeedbackTriggerSend record = findById(id);
		if (record != null) {
			repository.deleteById(id);
		}
	}

	@Override
	@Transactional
	public void batchDelete(List<String> ids) {
		if (ids != null && !ids.isEmpty()) {
			repository.deleteAllById(ids);
		}
	}

	// ==================== 业务查询方法 ====================

	@Override
	public List<FeedbackTriggerSend> findByTriggerRecordId(String triggerRecordId) {
		return repository.findByTriggerRecordId(triggerRecordId);
	}

	@Override
	public List<FeedbackTriggerSend> findBySendStatus(Boolean sendStatus) {
		return repository.findBySendStatus(sendStatus);
	}

	@Override
	public List<FeedbackTriggerSend> findByTriggerRecordIdAndSendStatus(String triggerRecordId, Boolean sendStatus) {
		return repository.findByTriggerRecordIdAndSendStatus(triggerRecordId, sendStatus);
	}

	@Override
	public List<FeedbackTriggerSend> findPendingSendRecords(Date expectedSendTime) {
		return repository.findByExpectedSendTimeBeforeAndSendStatusFalse(expectedSendTime);
	}

	@Override
	public List<FeedbackTriggerSend> findByExpectedSendTimeBetween(Date startTime, Date endTime) {
		return repository.findByExpectedSendTimeBetween(startTime, endTime);
	}

	// ==================== 状态更新方法 ====================

	@Override
	@Transactional
	public FeedbackTriggerSend updateSendStatus(String id, Boolean sendStatus, String sendResult) {
		FeedbackTriggerSend record = findById(id);
		record.setSendStatus(sendStatus);
		record.setSendResult(sendResult);
		if (sendStatus != null && sendStatus) {
			record.setSendTime(new Date());
		}
		return repository.save(record);
	}

	@Override
	@Transactional
	public FeedbackTriggerSend markAsSent(String id, String sendResult) {
		return updateSendStatus(id, true, sendResult);
	}

	@Override
	@Transactional
	public FeedbackTriggerSend markAsFailed(String id, String sendResult) {
		return updateSendStatus(id, false, sendResult);
	}

}
